package com.tem.customer.service.wechat;

import com.iplatform.common.utils.LogUtils;
import com.tem.customer.infrastructure.config.WechatApiProperties;
import com.tem.customer.model.dto.wechat.WechatAccessTokenResponse;
import com.tem.customer.model.dto.wechat.WechatGroupDetailRequest;
import com.tem.customer.model.dto.wechat.WechatGroupDetailResponse;
import com.tem.customer.shared.exception.BusinessException;
import com.tem.customer.shared.utils.RestClientUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * 企业微信API服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatApiServiceImpl implements WechatApiService {

    private final WechatApiProperties wechatApiProperties;

    private final StringRedisTemplate stringRedisTemplate;

    private final RestClientUtils restClientUtils;

    /**
     * Access Token缓存键前缀
     */
    private static final String ACCESS_TOKEN_CACHE_KEY = "wechat:access_token:";

    /**
     * 企业微信API URL常量
     */
    private static final String GET_TOKEN_URL = "/cgi-bin/gettoken";
    private static final String GET_GROUP_DETAIL_URL = "/cgi-bin/externalcontact/groupchat/get";

    @Override
    public WechatGroupDetailResponse getGroupDetail(WechatGroupDetailRequest request) {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        if (request == null || !StringUtils.hasText(request.getChatId())) {
            throw BusinessException.error("客户群ID不能为空");
        }

        try {
            String accessToken = getAccessToken();
            String uri = GET_GROUP_DETAIL_URL + "?access_token={token}";

            LogUtils.info(log, "调用企业微信获取群详情API，群ID: {}", request.getChatId());

            WechatGroupDetailResponse result = restClientUtils.postJson(
                    uri,
                    new Object[]{accessToken},
                    request,
                    WechatGroupDetailResponse.class
            );

            if (result == null) {
                throw BusinessException.error("企业微信API返回空响应");
            }

            if (!result.isSuccess()) {
                LogUtils.error(log, "企业微信获取群详情失败: {}", result.getErrorMessage());
                throw BusinessException.error("获取群详情失败: " + result.getErrorMessage());
            }

            LogUtils.info(log, "企业微信获取群详情成功，群ID: {}, 群名: {}",
                    request.getChatId(),
                    result.getGroupChat() != null ? result.getGroupChat().getName() : "未知");

            return result;

        } catch (Exception e) {
            LogUtils.error(log, "获取群详情异常", e);
            throw BusinessException.error("获取群详情失败: " + e.getMessage());
        }
    }

    @Override
    public WechatGroupDetailResponse getGroupDetail(String chatId, Integer needName) {
        return getGroupDetail(new WechatGroupDetailRequest(chatId, needName));
    }

    @Override
    public WechatGroupDetailResponse getGroupDetail(String chatId) {
        return getGroupDetail(new WechatGroupDetailRequest(chatId));
    }

    @Override
    public String getAccessToken() {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        String cacheKey = ACCESS_TOKEN_CACHE_KEY + wechatApiProperties.getCorpId();
        String cachedToken = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StringUtils.hasText(cachedToken)) {
            LogUtils.debug(log, "从缓存获取Access Token成功");
            return cachedToken;
        }

        return refreshAccessToken();
    }

    @Override
    public String refreshAccessToken() {
        if (!wechatApiProperties.isEnabled()) {
            throw BusinessException.error("企业微信API功能未启用");
        }

        if (!StringUtils.hasText(wechatApiProperties.getCorpId()) ||
                !StringUtils.hasText(wechatApiProperties.getCorpSecret())) {
            throw BusinessException.error("企业微信配置不完整，请检查corpId和corpSecret");
        }

        try {
            LogUtils.info(log, "获取企业微信Access Token");

            String uri = GET_TOKEN_URL + "?corpid={corpid}&corpsecret={secret}";
            Object[] uriVariables = {wechatApiProperties.getCorpId(), wechatApiProperties.getCorpSecret()};

            WechatAccessTokenResponse result = restClientUtils.get(
                    uri,
                    uriVariables,
                    WechatAccessTokenResponse.class
            );

            if (result == null) {
                throw BusinessException.error("企业微信API返回空响应");
            }

            if (!result.isSuccess()) {
                LogUtils.error(log, "获取Access Token失败: {}", result.getErrorMessage());
                throw BusinessException.error("获取Access Token失败: " + result.getErrorMessage());
            }

            String accessToken = result.getAccessToken();
            if (!StringUtils.hasText(accessToken)) {
                throw BusinessException.error("获取到的Access Token为空");
            }

            // 缓存Access Token，提前5分钟过期
            String cacheKey = ACCESS_TOKEN_CACHE_KEY + wechatApiProperties.getCorpId();
            long cacheTime = Math.max(wechatApiProperties.getTokenCacheTime() - 300, 300);
            stringRedisTemplate.opsForValue().set(cacheKey, accessToken, cacheTime, TimeUnit.SECONDS);

            LogUtils.info(log, "获取企业微信Access Token成功，缓存时间: {}秒", cacheTime);
            return accessToken;

        } catch (Exception e) {
            LogUtils.error(log, "获取Access Token异常", e);
            throw BusinessException.error("获取Access Token失败: " + e.getMessage());
        }
    }
}
